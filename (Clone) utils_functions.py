# Databricks notebook source
from matplotlib.colors import LogNorm, Normalize
# from matplotlib.gridspec import GridSpec
from scipy.signal import butter, filtfilt
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import os
from config import *
def gen_lpf(cutoff, fs, order=5):
    return butter(order, cutoff, fs=fs, btype='low', analog=False)
def butter_lowpass_filtfilt(data, cutoff, fs, order=5):
    # b,a =  butter(order, cutoff, fs=fs, btype='low', analog=False)
    b,a = gen_lpf(cutoff, fs, order=order)
    data_lpf = filtfilt(b, a, data)
    return data_lpf
def gen_period_strs(dtstart,dtend,periodstr='M'):
    # print(dtstart,dtend)
    '''
    return a dataframe of dstart and dend for each period: weekly or mothly
    '''
    dtstart = pd.to_datetime(dtstart)
    dtend   = pd.to_datetime(dtend).to_period('D').to_timestamp(how='end')
    dtdates =[]
    mstart = dtstart
    mend = dtstart.to_period(periodstr).to_timestamp(how='end')
    fmt='%Y-%m-%d %H:%M:%S'
    if mend>dtend: # less than one period
        dtdates.append([x.strftime(fmt) for x in [mstart,mend]])
    else:
        while mend <=dtend:
            # query(mstart,mend)
            dtdates.append([x.strftime(fmt) for x in [mstart,mend]])
            mstart = (mend+pd.Timedelta(days=1)).to_period(periodstr).to_timestamp(how='start')
            mend   = (mend+pd.Timedelta(days=1)).to_period(periodstr).to_timestamp(how='end')
        if mstart<dtend: # less than one period left
            # query(mstart,dtend)
            dtdates.append([x.strftime(fmt) for x in [mstart,mend]])
    df = pd.DataFrame(dtdates,columns=['dstart','dend'])
    # df = df.map(lambda x: pd.Timestamp.strftime(x,format='%Y-%m-%d %H:%M:%S'))
    # print (df)
    return df
def plot_hist2d(listofAX,listofAY,listofTitles,ftitle,pltlims=5):
    '''
    impurtlist of acc_x,  list of acc_y, list of subtitles
    '''
    nsets = len(listofAX)
    cjet = plt.cm.jet
    f,ax = plt.subplots(1,nsets,figsize=(nsets*6,6),sharex=True,layout='constrained')
    if nsets == 1:
        ax=[ax]
    f.suptitle(ftitle,fontweight='bold')
    pcs=[]
    for i in range(nsets):
        if nsets!=1:
           ax[i].set_title(listofTitles[i])
        pci=ax[i].hist2d(listofAY[i], listofAX[i], range =[[-1*pltlims, pltlims ],[pltlims*(-1), pltlims ]],bins=[100,100],cmap=cjet,norm=LogNorm(),density=True)

        ax[i].set_aspect('equal')
        ax[i].set_xlim(-1.1*pltlims,1.1*pltlims)
        ax[i].set_ylim(-1.1*pltlims,1.1*pltlims)
        ax[i].set_xlabel('Acc_Y(m/s$^2$)')
        ax[i].set_ylabel('Acc_X(m/s$^2$)')
        f.colorbar(pci[3],ax=ax[i])
    f.savefig(ftitle+'.png',dpi=200)
    plt.close()
    return ftitle+'.png'
def imshow_hist2d(dfh,info_dict={'plt_limits':[-5,5]}):
    ''' default columns
    expected columns(or the col_names dict defined it):
        Acc_X  -
        Acc_Y  -
        count - 1, if time history data as imported,
    '''
    if 'col_names' in info_dict.keys():
        dfh = dfh.rename(columns=info_dict['col_names'])
    if not 'count' in dfh.columns:
        dfh['count']=1

    if not ('axX10' in dfh.columns and 'ayX10' in dfh.columns):
        dfh['axX10'] = (dfh['Acc_X']*10.0).astype('int')
        dfh['ayX10'] = (dfh['Acc_Y']*10.0).astype('int')
    plt_lim = info_dict['plt_limits']
    dfh=dfh[ (dfh['axX10'].between(plt_lim[0]*10,plt_lim[1]*10))  & (dfh['ayX10'].between(plt_lim[0]*10,plt_lim[1]*10))]
    xyrng=range(int(plt_lim[0]*10),int(plt_lim[1]*10)+1)
    df2d = pd.DataFrame(index=xyrng,columns=xyrng)
    dfg  = dfh.groupby(['axX10','ayX10']).agg({'count':'sum'}).reset_index()
    dfp  = dfg.pivot(index='axX10', columns='ayX10', values='count')
    df2d.loc[dfp.index,dfp.columns]=dfp
    df2d=df2d.astype(float)
    df2d=df2d.sort_index(ascending=False)
    # len_cnt = len(dfg['count'].unique())
    axs = df2d.index.to_series().reset_index(drop=True)
    ays = df2d.columns.to_series().reset_index(drop=True)
    xticks  = axs[axs%20==0].index
    xlabels = axs[axs%20==0].values/10.0
    yticks  = ays[ays%20==0].index
    ylabels = ays[ays%20==0].values/10.0
    f,ax  = plt.subplots(1,1,figsize=(8,7),layout='constrained')
    title = info_dict['fname_no_ext']
    f.suptitle(title,fontweight='bold')
    im0=ax.imshow(df2d,cmap='jet',norm=LogNorm())
    ax.set_aspect('equal')
    ax.set_yticks(xticks, labels=xlabels)
    ax.set_xticks(yticks, labels=ylabels)
    ax.set_xlabel('AccY(m/s$^2$)')
    ax.set_ylabel('AccX(m/s$^2$)')
    f.colorbar(im0, ax=ax)
    f.savefig(title+'.png',dpi=200)
    print (title+'.png   ...   generated ...')
    plt.close()
def imshow_hist2d_from_dfs(dfs,info_dict={'plt_limits':[-5,5]}):
    ''' 
    two or more dataframes, each with Acc_X, Acc_Y, count columns
    default columns
    expected columns(or the col_names dict defined it):
        Acc_X  - or axX10
        Acc_Y  - or ayX10
        count - 1, if time history data as imported

        info_dict keys:  listofTitles, fname_no_ext, plt_limits,col_names
    '''

    nsets = len(dfs)
    # cjet = plt.cm.jet
    f,ax = plt.subplots(1,nsets,figsize=(nsets*6,6),sharex=True,layout='constrained')
    if nsets == 1:
        ax=[ax]
    title     = info_dict['fname_no_ext']    
    subtitles = info_dict['listoftitles']
    f.suptitle(title,fontweight='bold')    
    
    
    plt_lim = info_dict['plt_limits']
    xyrng=range(int(plt_lim[0]*10),int(plt_lim[1]*10)+1)

    for idf, dfh in enumerate(dfs):
    
        if 'col_names' in info_dict.keys():
            dfh = dfh.rename(columns=info_dict['col_names'][idf])
        
        if not 'count' in dfh.columns:
            dfh['count']=1

        if not ('axX10' in dfh.columns and 'ayX10' in dfh.columns):
            dfh['axX10'] = (dfh['Acc_X']*10.0).astype('int')
            dfh['ayX10'] = (dfh['Acc_Y']*10.0).astype('int')
        

        dfh=dfh[ (dfh['axX10'].between(plt_lim[0]*10,plt_lim[1]*10))  & (dfh['ayX10'].between(plt_lim[0]*10,plt_lim[1]*10))]

        df2d    = pd.DataFrame(index=ax,columns=ay)
        dfg     = dfh.groupby(['axX10','ayX10']).agg({'count':'sum'}).reset_index()
        dfp     = dfg.pivot(index='axX10', columns='ayX10', values='count')
        df2d.loc[dfp.index,dfp.columns]=dfp
        df2d    = df2d.astype(float)
        df2d    = df2d.sort_index(ascending=False)
        axs     = df2d.index.to_series().reset_index(drop=True)
        ays     = df2d.columns.to_series().reset_index(drop=True)
        xticks  = axs[axs%20==0].index
        xlabels = axs[axs%20==0].values/10.0
        yticks  = ays[ays%20==0].index
        ylabels = ays[ays%20==0].values/10.0

        ax[idf].set_title(subtitles[idf])
        im0=ax[idf].imshow(df2d,cmap='jet',norm=LogNorm())
        ax[idf].set_aspect('equal')
        ax[idf].set_yticks(xticks, labels=xlabels)
        ax[idf].set_xticks(yticks, labels=ylabels)
        ax[idf].set_xlabel('AccY(m/s$^2$)')
        ax[idf].set_ylabel('AccX(m/s$^2$)')
        f.colorbar(im0, ax=ax[idf])
    f.savefig(title+'.png',dpi=200)
    print (title+'.png   ...   generated ...')
    plt.close()
def fric_elipse_fix(df):
    '''
    expecting cols: ayX10, axX10, Acc_X, Acc_Y
    '''
    # if 'count' in df.columns:
    #     df['count']=1
    df['axX10'] = (df['Acc_X']*10).astype('int')
    df['ayX10'] = (df['Acc_Y']*10).astype('int')    
    dfy = df.groupby('ayX10')[['axX10']].quantile(0.99)
    if dfy.shape[0]<18:
        y0=np.interp(0,dfy.index,dfy['axX10'])
        dfy['axlpf'] =  dfy['axX10']*0+y0
    else:
        dfy['axlpf'] = butter_lowpass_filtfilt(dfy['axX10'],1,40)
    # lpf, elipse
    y0=np.interp(0,dfy.index,dfy['axlpf'])
    # dfy = df.groupby('ayX10')[['axX10']].quantile(0.99)
    # y0=20
    # dfy['axlpf'] =    dfy['axX10']*0+y0
    yelps = np.arange(-40,41)
    xelps = np.sqrt(1-yelps**2/40**2)*(y0+2)
    dfelp = pd.DataFrame(xelps,index=yelps,columns=['xprof'])
    dfelp = dfelp.reindex(dfelp.index.union(dfy.index)).fillna(0.2).reindex(dfy.index)
    # dfelp['diff']  = dfelp['xprof']-dfy['axX10']
    dfy['xprof']  = dfelp['xprof']
    dfy['scale']  = dfy['xprof']/dfy['axlpf']
    df['scale']   = np.interp(df['Acc_Y'],dfy.index/10.0,dfy['scale'])
    df['Acc_X_1'] = df['Acc_X']* df['scale'] 
    df['Acc_Y_1'] =   df['Acc_Y']
    df.loc[df['Acc_Y'].abs()>3.5,'Acc_Y_1']= df.loc[df['Acc_Y'].abs()>3.5,'Acc_Y'] *  df.loc[df['Acc_Y'].abs()>3.5,'scale']
    return df[['Acc_X','Acc_X_1','Acc_Y','Acc_Y_1',]].copy()
    # return dfn    
    # return df[['Acc_X','Acc_X_1','Acc_Y']].copy()
    # return dfn        
def gen_wfeq_from_g2ak(df,dict_info):
   
                         #----  : g2ak cols  
    DICT_HISTO_RENAME_0={
                        'speed' : 'Speed',
                    'longitude' : 'Longitude',
                     'latitude' : 'Latitude',
                            'x' : 'CoordX',
                            'y' : 'CoordY',
                     'datetime' : 'DateTime',
            'dist_gps_interval' : 'Dist_GPS',
            'dist_spd_interval' : 'Dist_Speed',
                   'pred_speed' : 'Count',
                   'wfeq_acc_x' : 'Acc_X',
                   'wfeq_acc_y' : 'Acc_Y',
                     'datetime' : 'DateTime'
              }

    DICT_HISTO_AGG = {
                     'DateTime' : 'mean',
                        'Count' : 'sum',
                    #   'Acc_X_1' : 'mean',
                    #   'Acc_Y_1' : 'mean',
                     'Dist_GPS' : 'sum',
                   'Dist_Speed' : 'sum',
                    'Latitude'  : 'mean',
                   'Longitude'  : 'mean',
                      'CoordX'  : 'mean',
                      'CoordY'  : 'mean'
                                       }

    limit=dict_info['limits']
    df=df[ (df['Acc_X'].between(limit[0],limit[1]))  & (df['Acc_Y'].between(limit[0],limit[1]))].reset_index()
    df['Count']=1
    dfn = fric_elipse_fix(df[['Acc_X','Acc_Y']].copy())


    df['kph']    = (df['Speed']*3.60).round(0).astype(int)
    df['tenXax'] = (dfn['Acc_X_1']*10.0).round(0).astype(int)
    df['tenXay'] = (dfn['Acc_Y_1']*10.0).round(0).astype(int)
    # df['Date']   = df['DateTime'].dt.strftime('%Y-%m-%d')
    dfgrp = df.groupby(by=['GroupId','GroupName','SerialNumber','Date','kph','tenXax','tenXay']).agg(DICT_HISTO_AGG).reset_index()
    # dfgrp = dfgrp.reset_index(drop=True)
    dfgrp['Speed']  = dfgrp['kph']/3.6 
    dfgrp['Acc_X']  = dfgrp['tenXax'] /10.0
    dfgrp['Acc_Y']  = dfgrp['tenXay'] /10.0
    # dfgrp=dfgrp.rename(columns=DICT_HISTO_RENAME)
    # timehistory
    # GroupId,GroupName,SerialNumber,DateTime,MessageType,Date,Latitude,Longitude,CoordX,CoordY,Speed,Acc_X,Acc_Y,Dist_GPS,Dist_Speed    
    # histogram
    # GroupId,GroupName,SerialNumber,DateTime,MessageType,Date,Latitude,Longitude,Speed,CoordX,CoordY,Count,Acc_X,Acc_Y,Dist_GPS,Dist_Speed,CreatedDateTime
    latlon_cols = ['Longitude','Latitude']
    float_cols = ['Speed', 'CoordX','CoordY', 'Acc_X','Acc_Y','Dist_GPS','Dist_Speed']
    dfgrp [float_cols]  =  dfgrp [float_cols].round(3)
    dfgrp [latlon_cols] =  dfgrp [latlon_cols].round(7)
    dfgrp ['DateTime']  =  dfgrp ['DateTime'].dt.round('1s')
    return dfgrp
def write_wfeq_histogram(df,dict_info):
    # tm_sandbox_catalog.akrondl_wear.kl_g2ak_acceleration_wfeq_histo
    SCHEMA_HISTOGRAM = """ CREATE TABLE  kl_g2ak_acceleration_wfeq_histo (
        GroupId            string comment 'Group ID',
        GroupName          string comment 'Group name',
        SerialNumber       string comment 'Serial number of the device Geotab or Xee',
        DateTime        timestamp comment 'mean time of current combination record',
        MessageType        string comment 'Histogram Data Webfleet Equivalent',  
        Date                 date comment 'Data Capture Date',
        Latitude           double comment 'Mean latitude  of the day - degree',
        Longitude          double comment 'Mean longitude of the day - degree',
        Speed              double comment 'Speed  -  m/s',
        CoordX             double comment 'Longitudinal location - m',
        CoordY             double comment 'latidute location - m ',
        Count              int    comment 'Count of histogram data,value of a sparse 3D matrix',
        Acc_X              double comment 'Longitudinal acceleration in m/sec^2',
        Acc_Y              double comment 'Lateral acceleration in m/sec^2',
        Dist_GPS           double comment 'sum of the distance drive (m) from GPS calculation',
        Dist_Speed         double comment 'sum of the distance of drive (m) from speed calculation',
        CreatedDateTime timestamp comment 'timestamp of when data ingested');"""
 
    '''
    df : calculated result from direct output from G2AK
    dict_info={'csvname':csvname,'serialnumber':serialnumber,'groupname':groupname}
    csvname: csv file name if the calculation is saved as csvform
    the order: process df first, if df empty, then use the csv file

    '''
    schema=SCHEMA_HISTOGRAM
    tablename = schema.split('\n')[0].split('TABLE')[1].strip().split('(')[0].strip()
    schema_txts = [x.strip() for x in schema.split('\n')[1:]] # exclude the CREATE TABLE
    col_names=[x.split()[0].strip() for x in schema_txts]
    col_types=[x.split()[1].strip() for x in schema_txts]
    schemastr=','.join( [x+' '+y for x,y in zip(col_names,col_types)])

    if df.shape[0]==0:
        return False

    # df['DateTime']         =  df['DateTime'].dt.strftime('%Y-%m-%d %H:%M:%S')
    # df['CreatedDateTime']  =  pd.Timestamp('now').strftime('%Y-%m-%d %H:%M:%S')
    df['CreatedDateTime']  =  pd.Timestamp('now')
    df['MessageType']      = 'Webfleet Equivalent Histogram'
    # df['GroupId']          = dict_info['groupid']
    # df['GroupName']        = dict_info['groupname']
    # df['Date']             =  df['DateTime'].dt.date
    df = df[col_names]
    # display(df)
    # col_names.sort()
    # b=list(df.columns)
    # b.sort()
    # print('requires')
    # print(col_names)
    # print('existing')
    # print(b)

    fulltablename = '.'.join(dict_info['schema_hierarchy'] + [tablename] )
    print (df.shape, f'write acceleration histogram to table ...{fulltablename}')
    if 'linux' in dict_info['platform']:
        spark_df = spark.createDataFrame(df,schema=schemastr)
        spark_df.write.format("delta").mode("append").saveAsTable(fulltablename)


def gen_imgsrc_txt(pngdir,fltstr=''):
    txts=[]
    if not os.path.isdir(pngdir):
        print('plese specify a valid path which contains png filrs')
        pngdir = '/Workspace/Users/<USER>/G2AK_bancroft'
        
    flist=os.listdir(pngdir)
    fltstr='fric'
    print('%md')
    for f in flist:
        if f.endswith('.png'):

            if 1:
                fpath = os.path.join(pngdir,f)
                print(f"<img src='{fpath}'>")
                txts.append(f"<img src='{fpath}'>")
    return '\n'.join(txts)
# Penske Logistics	b28B5
def get_current_status(groupid):

    # dflog,dfg2ak = get_current_status(groupid)
    sqltxtlog = f""" select serialnumber, vehicleidentificationnumber as vin, max(date) as log_max_date, min(date) as log_min_date
                    from {LOGRECORD} where  groupid='{groupid}' group by serialnumber, vin"""
    sqltxtg2ak = f""" select serialnumber, vin, max(date) as g2ak_max_date, min(date) as g2ak_min_date
                  from {G2AK_DB_SCHEMA}.{TABLE_TIMEHISTORY} where  groupid='{groupid}' group by serialnumber, vin """                
    # dfreq = spark.sql(sql
    dflog = spark.sql(sqltxtlog).toPandas()
    dfg2ak = spark.sql(sqltxtg2ak).toPandas()
    print (dflog.shape)
    print (dfg2ak.shape)
    dfm=pd.merge(dflog,dfg2ak,on=['serialnumber','vin'],how='left')
    dfm['datestart']=dfm['log_min_date']
    dfm.loc[ dfm['g2ak_max_date']>dfm['log_min_date'], 'datestart']=dfm['g2ak_max_date']+pd.Timedelta(days=1)
    dfm['dateend']=dfm['log_max_date']-pd.Timedelta(days=3)
    display(dfm)
    return dfm                    
