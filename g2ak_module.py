# Databricks notebook source
import pyproj
from filterpy.common import Q_discrete_white_noise
from filterpy.kalman import ExtendedKalmanFilter

import os,sys,platform
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LogNorm
from matplotlib.gridspec import GridSpec
from matplotlib.dates import DateFormatter
from scipy.linalg import block_diag
from scipy.signal import butter, filtfilt

# # # = =  Kejing Li 2024-12-05 09:58:35  Thu WK#: 49 = =  # # #
# address two major issues:
# 1 - process need to be cmpatible with geotab firmware changes
# 2 - NTB-6060 like tables with filling 0 speeds
# Add histogram output dataframe
__version__='3.0'
__str__='''
    = = GPS to Acceleration Calculation with Kalman Filter = =

  ,ad8888ba,       ad888888b,            db            88      a8P
 d8"'    `"8b     d8"     "88           d88b           88    ,88'
d8'                       a8P          d8'`8b          88  ,88"
88                     ,d8P"          d8'  `8b         88,d88'
88      88888        a8P"            d8YaaaaY8b        8888"88,
Y8,        88      a8P'             d8""""""""8b       88P   Y8b
 Y8a.    .a88     d8"              d8'        `8b      88     "88,
  `"Y88888P"      88888888888     d8'          `8b     88       Y8b
'''
G2AK_STR=__str__
HMFORMATTER = DateFormatter('%H:%M')
EMPTY_DF =pd.DataFrame()

LOGRECORD = 'tm_sandbox_catalog.geotab_sales_eng.logrecord'
SRC_TABLE = LOGRECORD
# output tables
G2AK_DB_SCHEMA       = 'wear10_sandbox.g2ak'
TABLE_HISTOGRAM      = 'acceleration_histogram'
TABLE_TIMEHISTORY    = 'acceleration_timehistory'
TABLE_G2AKSTAT       = 'calculation_status'
TABLE_REQUEST        = 'calculation_request'

SCHEMA_TIMEHISTORY = f"""     CREATE TABLE IF NOT EXISTS {G2AK_DB_SCHEMA}.{TABLE_TIMEHISTORY} (
        groupid            string comment 'group id',
        groupname          string comment 'group name',
        serialnumber       string comment 'serial number of the device geotab or xee',
        vin                string comment 'vehicle identification number',
        date                 date comment 'data capture date',
        datetime        timestamp comment 'timestamp of the record',
        messagetype        string comment 'time history data',
        latitude           double comment 'latitude   - degree',
        longitude          double comment 'longitude  - degree',
        coordx             double comment 'latitude coordinate  - m',
        coordy             double comment 'longitude coordinate  - m',
        speed              double comment 'speed   - m/s',
        acc_x              double comment 'longitudinal acceleration  - m/sec^2,webfleet equivialent',
        acc_y              double comment 'lateral acceleration  - m/sec^2,webfleet equivialent',
        dist_gps           double comment 'distance drive for current interval from gps distance- m',
        dist_speed         double comment 'distance drive for current interval from speed x time- m',
        ax_g2ak            double comment 'longitudinal acceleration  - m/sec^2',
        ay_g2ak            double comment 'lateral acceleration  - m/sec^2',
        createdtime     timestamp comment 'timestamp of when data ingested')"""

SCHEMA_HISTOGRAM  = f"""  CREATE TABLE IF NOT EXISTS {G2AK_DB_SCHEMA}.{TABLE_HISTOGRAM} (
        groupid            string comment 'group id',
        groupname          string comment 'group name',
        serialnumber       string comment 'serial number of the device geotab or xee',
        vin                string comment 'vehicle identification number',
        date                 date comment 'data capture date',
        datetime        timestamp comment 'mean time of current combination record',
        messagetype        string comment 'histogram data',
        latitude           double comment 'mean latitude  of the day - degree',
        longitude          double comment 'mean longitude of the day - degree',
        speed              double comment 'speed  -  m/s',
        acc_x              double comment 'longitudinal acceleration in m/sec^2',
        acc_y              double comment 'lateral acceleration in m/sec^2',
        count                 int comment 'count of histogram data,value of a sparse 3d matrix',
        dist_gps           double comment 'sum of the distance drive (m) from gps calculation',
        dist_speed         double comment 'sum of the distance of drive (m) from speed calculation',
        createdtime     timestamp comment 'timestamp of when data ingested')"""

SCHEMA_G2AKSTAT = f"""  CREATE TABLE  IF NOT EXISTS {G2AK_DB_SCHEMA}.{TABLE_G2AKSTAT} (
        groupid            string comment 'group id',
        serialnumber       string comment 'serial number of the device geotab or xee',
        groupname          string comment 'group name',
        date                 date comment 'data capture date',
        vin                string comment 'vehicle identification number',
        messagetype        string comment 'conversion file',
        category           string comment 'analytic variables',
        nspdchanges           int comment 'mumber of records of speed changes',
        meanval            double comment 'average value of current variable',
        stddev             double comment 'standard deviation current variable',
        minval             double comment 'min value of current variable',
        pct25val           double comment '25 percent value of current variable',
        pct50val           double comment 'median value of current variable',
        pct75val           double comment '75 percent value of current variable',
        maxval             double comment 'max value of current variable',
        nspdnonzero           int comment 'number of records of non-zero speed',
        nrecords              int comment 'number of records of that day and that serial number',
        nhist                 int comment 'number of records of time history',
        ntseq                 int comment 'number of records of histogram',
        utmzone               int comment 'longidtude zone location',
        firmware_cat       string comment 'issues with the firmware',
        dist_gps           double comment 'distance drive for current interval from gps distance- m',
        dist_spd           double comment 'distance drive for current interval from speed x time- m',
        dist_diff_pct      double comment '(dist_spd-dist_gps) / dist_spd * 100',
        createdtime     timestamp comment 'timestamp of when data ingested' )"""

SCHEMA_REQUEST = f"""  CREATE TABLE  IF NOT EXISTS {G2AK_DB_SCHEMA}.{TABLE_REQUEST} (
         requestdate      date  comment 'date when requested',
           requestor    string  comment 'the person who requested',
             groupid    string  comment 'geotab group id',
           groupname    string  comment 'geotab group name ',
        serialnumber    string  comment 'geotab serial number',
           datestart      date  comment 'first day to calculate',
             dateend      date  comment 'last  day to calculate',
              status    string  comment 'current status of this request',
        createdtime  timestamp  comment 'timestamp of when data ingested' ) """

def visual_xy_speed(df,title):
    fig = plt.figure(layout="constrained",figsize=(16,7))
    fig.suptitle(title,fontweight='bold')
    gs = GridSpec(3, 6, figure=fig)
    ax0 = fig.add_subplot(gs[:, 0])
    ax2 = fig.add_subplot(gs[0, 1:])
    ax3 = fig.add_subplot(gs[1, 1:],sharex = ax2)
    ax4 = fig.add_subplot(gs[2, 1:],sharex = ax2)

    ax=[ax2,ax3,ax4]
    ax0.scatter(df['x'], df['y'],s=2,label='GPS Loacation',color='gray')
    ax0.set_aspect('equal')
    ax0.legend()
    ax[0].scatter(df.index,df['speed'],s=2,label='speed')
    ax[0].set_ylabel('Speed (m/s)')
    ax[0].legend(loc='upper left')
    ax[1].scatter(df.index,df['dist_gps_interval'],label='dist_gps_interval',  s=2)
    ax[1].scatter(df.index,df['dist_spd_interval'],label='dist_spd_interval',  s=1)
    ax[2].scatter(df.index,df['dist_gps_cumsum'],label='dist_gps_cumsum',s=2)
    ax[2].scatter(df.index,df['dist_spd_cumsum'],label='dist_spd_cumsum',s=1)
    for i in range(3):
        ax[i].legend()
        ax[i].xaxis.set_major_formatter(HMFORMATTER)
    fig.savefig(title,dpi=200)
    plt.close()
    return title+'.png'

def visual_g2ak_acc(df,title):
    fig = plt.figure(layout="constrained",figsize=(16,7))
    fig.suptitle(title,fontweight='bold')
    gs = GridSpec(3, 6, figure=fig)
    ax0 = fig.add_subplot(gs[:, 0])
    ax2 = fig.add_subplot(gs[0, 1:])
    ax3 = fig.add_subplot(gs[1, 1:], sharex = ax2)
    ax4 = fig.add_subplot(gs[2, 1:], sharex = ax2)
    ax=[ax2,ax3,ax4]

    if title.endswith('_g2ak'):
        prefix='pred_'
    elif title.endswith('_wfeq'):
        prefix='wfeq_'

    ax0.scatter(df['x'], df['y'],s=2,label='GPS Loacation')
    ax0.plot(df['pred_x'], df['pred_y'],label='Predict',color='r',linestyle='--')
    ax0.set_aspect('equal')
    ax0.legend()

    ax[0].scatter(df.index,df['speed'],s=4,label='org_speed')
    ax[0].scatter(df.index,df['pred_speed'],label='pred_speed',color='g',s=1)
    ax[0].set_ylabel('Speed (m/s)')

    ax[1].plot(df.index,df['speed']*0.0,label='accel=0.0',color='gray',linestyle='dashed')
    ax[1].scatter(df.index,df[f'{prefix}acc_x'],label=f'{prefix}acc_x',s=1.5,alpha=0.4,color='r')
    ax[1].set_ylabel('AccX (m/s$^2$)')
    ax[1].legend(loc='upper right')

    ax[2].plot(df.index,df['speed']*0.0,label='accel=0.0',color='gray',linestyle='dashed')
    ax[2].scatter(df.index,df[f'{prefix}acc_y'],label=f'{prefix}acc_y',s=1.5,alpha=0.4,color='r')
    ax[2].set_ylabel('AccY. (m/$^2$))')
    ax[2].set_xlabel('timestamp')

    for i in range(3):
        ax[i].legend()
        ax[i].xaxis.set_major_formatter(HMFORMATTER)
    fig.savefig(title,dpi=200)
    plt.close()

    return title+'.png'

def visual_hist2d(listofAX,listofAY,listofTitles,ftitle):
    '''
    list of acc_x,  list of acc_y, list of subtitles

    '''
    nsets = len(listofAX)
    cjet = plt.cm.jet
    f,ax = plt.subplots(1,nsets,figsize=(nsets*6,6),sharex=True,layout='constrained')
    if nsets == 1:
        ax=[ax]
    f.suptitle(ftitle,fontweight='bold')
    pcs=[]
    for i in range(nsets):
        if nsets!=1:
           ax[i].set_title(listofTitles[i])
        pci=ax[i].hist2d(listofAY[i], listofAX[i], range =[[-8, 8 ],[-8, 8 ]],bins=[200,200],cmap=cjet,norm=LogNorm(),density=True)
        # pcs.append(pci)
        # pc2=ax[1].hist2d(listofAY[0], listofAX[0],  range =[[-6 , 6 ],[-6 , 6 ]], bins=[ 100 , 100 ], cmap=cjet,norm=LogNorm() )
        ax[i].set_aspect('equal')
        ax[i].set_xlim(-6,6)
        ax[i].set_ylim(-6,6)
        ax[i].set_xlabel('Acc_Y(m/s$^2$)')
        ax[i].set_ylabel('Acc_X(m/s$^2$)')
        f.colorbar(pci[3],ax=ax[i])
        # f.colorbar(pc2[3],ax=ax[1],fraction=0.046, pad=0.04)
        # f.tight_layout()
    f.savefig(ftitle+'.png',dpi=200)
    plt.close()
    return ftitle+'.png'



def write_timehistory_table(df,dict_info):

    '''
    df : direct output from G2AK
    dict_info={'csvname':csvname,'serialnumber':serialnumber,'groupname':groupname}
    csvname: csv file name if the calculation is saved as csvform
    the order: process df first, if df empty, then use the csv file

    '''
    schema=SCHEMA_TIMEHISTORY
    tablename   = schema.split('\n')[0].lower().split('if not exists')[1].strip().split('(')[0].strip()
    schema_txts = [x.strip() for x in schema.split('\n')[1:]] # exclude the CREATE TABLE
    col_names   = [x.split()[0].strip() for x in schema_txts]
    col_types   = [x.split()[1].strip() for x in schema_txts]
    schemastr   = ','.join( [x+' '+y for x,y in zip(col_names,col_types)])

    if df.shape[0]==0:
        return False
    # display(df)
    # df['DateTime']         =  df['DateTime'].dt.strftime('%Y-%m-%d %H:%M:%S')
    # df['CreatedDateTime']  =  pd.Timestamp('now').strftime('%Y-%m-%d %H:%M:%S')
    df['createdtime']      =  pd.Timestamp('now')
    df['messagetype']      = 'time history'
    df['groupid']          = dict_info['groupid']
    df['groupname']        = dict_info['groupname']
    df['date']             =  dict_info['datestr']
    df['vin']              =  dict_info['vin']
    df['date']             =  pd.to_datetime(df['date']).dt.date
    df = df[col_names]
    print (dict_info['serialnumber'],dict_info['datestr'], df.shape, f'write time history to table ...{tablename}')

    if 'linux' in dict_info['platform']:
        spark_df=spark.createDataFrame(df,schema=schemastr)
        spark_df.write.format("delta").mode("append").saveAsTable(tablename)
    # display(df)


    if dict_info['csv_tseq']:
        csv_name='_'.join([dict_info['serialnumber'],dict_info['datestr'],'tseq.csv'])
        df.to_csv(csv_name,index=False)


def write_histogram_table(df,dict_info):

    '''
    df : calculated result from direct output from G2AK
    dict_info={'csvname':csvname,'serialnumber':serialnumber,'groupname':groupname}
    csvname: csv file name if the calculation is saved as csvform
    the order: process df first, if df empty, then use the csv file

    '''
    schema=SCHEMA_HISTOGRAM
    # tablename = schema.split('\n')[0].split('TABLE')[1].strip().split('(')[0].strip()
    tablename   = schema.split('\n')[0].lower().split('if not exists')[1].strip().split('(')[0].strip()
    schema_txts = [x.strip() for x in schema.split('\n')[1:]] # exclude the CREATE TABLE
    col_names=[x.split()[0].strip() for x in schema_txts]
    col_types=[x.split()[1].strip() for x in schema_txts]
    schemastr=','.join( [x+' '+y for x,y in zip(col_names,col_types)])
    if df.shape[0]==0:
        return False

    # df['DateTime']         =  df['DateTime'].dt.strftime('%Y-%m-%d %H:%M:%S')
    # df['CreatedDateTime']  =  pd.Timestamp('now').strftime('%Y-%m-%d %H:%M:%S')
    df['createdtime']  =  pd.Timestamp('now')
    df['messagetype']      =  'histogram data'
    df['groupid']          =  dict_info['groupid']
    df['groupname']        =  dict_info['groupname']
    df['date']             =  dict_info['datestr']
    df['vin']              =  dict_info['vin']
    df['date']             =  pd.to_datetime(df['date']).dt.date
    df = df[col_names]

    print (dict_info['serialnumber'],dict_info['datestr'], df.shape, f'write acceleration histogram to table ...{tablename}')
    if 'linux' in dict_info['platform']:
        spark_df = spark.createDataFrame(df,schema=schemastr)
        spark_df.write.format("delta").mode("append").saveAsTable(tablename)
    if dict_info['csv_hist']:
        csv_name='_'.join([dict_info['serialnumber'],dict_info['datestr'],'hist.csv'])
        df.to_csv(csv_name,index=False)




def write_g2akstat_table(df,dict_info):

    '''
    df : direct output from G2AK
    dict_info={'csvname':csvname,'serialnumber':serialnumber,'groupname':groupname}
    csvname: csv file name if the calculation is saved as csvform
    the order: process df first, if df empty, then use the csv file

    '''



    schema=SCHEMA_G2AKSTAT
    # tablename   = schema.split('\n')[0].split('TABLE')[1].strip().split('(')[0].strip()
    tablename   = schema.split('\n')[0].lower().split('if not exists')[1].strip().split('(')[0].strip()
    schema_txts = [x.strip() for x in schema.split('\n')[1:]] # exclude the CREATE TABLE
    col_names   = [x.split()[0].strip() for x in schema_txts]
    col_types   = [x.split()[1].strip() for x in schema_txts]
    schemastr   = ','.join( [x+' '+y for x,y in zip(col_names,col_types)])
    if df.shape[0]==0:
        return False
    # df['DateTime']         =  df['DateTime'].dt.strftime('%Y-%m-%d %H:%M:%S')
    # df['CreatedDateTime']  =  pd.Timestamp('now').strftime('%Y-%m-%d %H:%M:%S')
    df['createdtime']  =  pd.Timestamp('now')
    df['messagetype']      = 'g2ak calc log'
    df['groupid']          = dict_info['groupid']
    df['groupname']        = dict_info['groupname']
    df['date']             = dict_info['datestr']
    df['date']             =  pd.to_datetime(df['date']).dt.date
    df['vin']              =  dict_info['vin']

    df = df[col_names]

    # col_names.sort()
    # b=list(df.columns)
    # b.sort()
    # print('requires')
    # print(col_names)
    # print('existing')
    # print(b)

    # fulltablename = '.'.join(dict_info['schema_hierarchy'] + [tablename] )
    print (dict_info['serialnumber'],dict_info['datestr'], df.shape, f'write g2ak log to table ...{tablename}')
    if 'linux' in dict_info['platform']:
        spark_df=spark.createDataFrame(df,schema=schemastr)
        spark_df.write.format("delta").mode("append").saveAsTable(tablename)

# Calculation
def calc_deg2xy(df:pd.DataFrame):

    '''
    calculate:
     - x y location from the latitude and longitude
     - dtime and dspeed for statstics analysis
    requires: latitude, longitude,speed,index/datetime
    output new columumns:  x,  y, dist_gps_interval

    '''

    if (not 'x' in df.columns) or (not 'y' in df.columns):
        zone = int((186+ df['longitude'].mean() )/6)
        degdec2coord = pyproj.Proj(proj='utm', zone=zone, ellps='WGS84')
        xycoord = degdec2coord(df['longitude'],df['latitude'])
        df[['x','y']] = np.array(xycoord).T
    if 'datetime' in df.columns:
        df['dtime']   =  df['datetime'].diff().dt.total_seconds().fillna(-1)
    else:
        df['dtime']   =  df.index.to_series().diff().dt.total_seconds().fillna(-1)
    df['dspeed']  =  df['speed'].diff().fillna(0)
    df['dx'] = (df['x'].diff()).fillna(0)
    df['dy'] = (df['y'].diff()).fillna(0)

    df['dist_gps_interval'] = np.sqrt(df['dx']*df['dx']+df['dy']*df['dy'])
    df['dist_spd_interval'] = df['dtime']*df['speed']
    df['diff_dist'] = df['dist_spd_interval'] - df['dist_gps_interval']
    df['dist_spd_cumsum']   = df['dist_spd_interval'].cumsum()
    df['dist_gps_cumsum']   = df['dist_gps_interval'].cumsum()

    return df

def gen_lpf(cutoff, fs, order=5):
    return butter(order, cutoff, fs=fs, btype='low', analog=False)
def butter_lowpass_filtfilt(data, cutoff, fs, order=5):
    # b,a =  butter(order, cutoff, fs=fs, btype='low', analog=False)
    b,a = gen_lpf(cutoff, fs, order=order)
    data_lpf = filtfilt(b, a, data)
    return data_lpf
def EKF_HJACOBIAN_AT(x):
    vx = x[1,0]
    vy = x[4,0]
    spd = np.sqrt(vx**2 + vy**2)
    return np.array([[1,     0,  0,  0,      0,  0],
                     [0,     0,  0,  1,      0,  0],
                     [0, vx/spd, 0,  0, vy/spd,  0]])
def EKF_HX(x):
    return np.array([x[0,0], x[3,0], np.sqrt(x[1,0]**2+x[4,0]**2)]).reshape(-1,1)

def Kalman_EKF(df,freq=1):
    '''
        requires:  x, y, and  speed
        unit:      m, m, and  m/sec
        x: sta_var  = ['x','vx','ax','y','vy','ay']
        z: mea_var  = [x,y,speed]

    '''
    h,w = df.shape
    dtime = 1.0/freq
    hfdsq = 0.5*dtime**2
    kf = ExtendedKalmanFilter(dim_x=6, dim_z=3)
    kf.R  = np.diag([25, 25, 0.02])
    kf.F  = np.array([  [1,   dtime, hfdsq,   0,     0,        0],
                        [0,       1, dtime,   0,     0,        0],
                        [0,       0,     1,   0,     0,        0],
                        [0,       0,     0,   1, dtime,    hfdsq],
                        [0,       0,     0,   0,      1,   dtime],
                        [0,       0,     0,   0,      0,       1]])
    kf.P = np.diag([50, 50, 50, 50, 50, 50])
    kf.x = np.array([df['x'].iloc[0], 0.1, 0, df['y'].iloc[0], 0.1, 0]).reshape(-1,1)
    q = Q_discrete_white_noise(dim=3, dt=dtime, var=0.5)*0.1
    kf.Q = block_diag(q, q)
    sta_var = ['x','vx','ax','y','vy','ay']
    mea_var = ['x','y','speed']
    xs = []
    arr_z = df[mea_var].to_numpy()
    h,w = arr_z.shape
    for i in range(h):
        z = arr_z[i:i+1,:].T
        kf.update(z, EKF_HJACOBIAN_AT, EKF_HX)
        xs.append(kf.x)
        kf.predict()
    predictions = np.array(xs).squeeze(axis=2)
    df_pred = pd.DataFrame(predictions,columns=['pred_'+ss for ss in sta_var])
    df_pred = df_pred.set_index(df.index)
    df_pred['pred_speed']=np.sqrt(predictions[:,1]*predictions[:,1]+predictions[:,4]*predictions[:,4])

    df=pd.merge(df,df_pred,left_index=True,right_index=True)
    # df['pred_acc_y'] = (df['pred_ax']*df['pred_vy'] - df['pred_ay']*df['pred_vx'])/df['pred_speed']
    df['pred_acc_y'] = (-1*df['pred_ax']*df['pred_vy'] + df['pred_ay']*df['pred_vx'])/df['pred_speed']
    df['pred_acc_x'] = (df['pred_ax']*df['pred_vx'] + df['pred_ay']*df['pred_vy'])/df['pred_speed']
    df=df[ (df['pred_acc_x'].between(-6.5,6.5) ) & (df['pred_acc_y'].between(-6.5,6.5))  & (df['speed']<160/3.60)]
    # use gps to fix speed, when speed>45 gps calculation will different from speed record
    return df

def g2ak_calculation(df,dict_info):

    DICT_TIMESEQ_RENAME = {
                        'x'    :  'coordx',
                        'y'    :  'coordy',
               'wfeq_acc_x'    :  'acc_x',
               'wfeq_acc_y'    :  'acc_y',
               'pred_acc_x'    :  'ax_g2ak',
               'pred_acc_y'    :  'ay_g2ak',
                'longitude'    :  'longitude',
                 'latitude'    :  'latitude',
                    'speed'    :  'speed',
                 'datetime'    :  'datetime',
        'dist_gps_interval'    :  'dist_gps',
        'dist_spd_interval'    :  'dist_speed',    }

    DICT_HISTO_RENAME={
                        'speed' : 'speed',
                    'longitude' : 'longitude',
                     'latitude' : 'latitude',
                            'x' : 'coordx',
                            'y' : 'coordy',
            'dist_gps_interval' : 'dist_gps',
            'dist_spd_interval' : 'dist_speed',
                   'pred_speed' : 'count',
                   'wfeq_acc_x' : 'acc_x',
                   'wfeq_acc_y' : 'acc_y',         }


    DICT_HISTO_AGG = {
                     'datetime' : 'mean',
                        'speed' : 'mean',
                   'pred_speed' : 'count',
                   'wfeq_acc_x' : 'mean',
                   'wfeq_acc_y' : 'mean',
            'dist_gps_interval' : 'sum',
            'dist_spd_interval' : 'sum',
                    'latitude'  : 'mean',
                   'longitude'  : 'mean',
                           'x'  : 'mean',
                           'y'  : 'mean'           }




    freq =dict_info['freq']
    if df.shape[0]<50:
        print (dict_info['serialnumber'],dict_info['datestr'],df.shape,'not enough data points to g2ak ... ')
        return {'df_time':pd.DataFrame(),'df_hist':pd.DataFrame()}

    print (dict_info['serialnumber'],dict_info['datestr'],df[df['speed']>0].shape,'continue to g2ak ... ')
    dfekf = Kalman_EKF(df,freq=freq)
    # dfekf=dfekf[[ 'latitude','longitude','x','y','speed', 'pred_x','pred_y','pred_speed','pred_acc_x','pred_acc_y','dist_gps_interval','dist_spd_interval']]

    if dict_info['png_g2ak']:
        plt_title='_'.join([dict_info['serialnumber'],dict_info['datestr'],'g2ak'])
        png = visual_g2ak_acc(dfekf,plt_title)

    # if dict_info['csv_g2ak']:
    #     csvname ='_'.join([dict_info['serialnumber'],dict_info['datestr'],'g2ak.csv'])
    #     dfekf.to_csv(csvname)
    # dfekf=dfekf[dfekf['speed']>0.5/3.6] # 0.5kph will be rounded to 0.0
    # dfekf['speed']>0.5/3.6
    # h1 = dfekf[dfekf['speed']>0.5/3.6].shape[0]
    # dfekf = dfekf[dfekf['speed']>0.5/3.6].copy()
    h1=dfekf.shape[0]
    if h1 <10:
        print (dict_info['serialnumber'],dict_info['datestr'],h1,'not enough valid data points to project to wfeq... ')
        #    e.g. G9C521219F62, 2024-07-06, same speed 1 kph
        return {'df_time':pd.DataFrame(),'df_hist':pd.DataFrame()}
    #  _____   _____  ___  ___  _____   ______   _____  ___  ___   ___    _____   _   _
    # |_   _| |_   _| |  \/  | |  ___|  |  _  \ |  _  | |  \/  |  / _ \  |_   _| | \ | |
    #   | |     | |   | .  . | | |__    | | | | | | | | | .  . | / /_\ \   | |   |  \| |
    #   | |     | |   | |\/| | |  __|   | | | | | | | | | |\/| | |  _  |   | |   | . ` |
    #   | |    _| |_  | |  | | | |___   | |/ /  \ \_/ / | |  | | | | | |  _| |_  | |\  |
    #   \_/    \___/  \_|  |_/ \____/   |___/    \___/  \_|  |_/ \_| |_/  \___/  \_| \_/
    lpf20=dict_info['lpf20']
    lpf05=dict_info['lpf05']
    # if dict_info['firmware_cat'] =='G9F821184285_2024-11-20':
        # lpf20=dict_info['lpf50']
    # if dict_info['firmware_cat'] =='G9107582NNCK_2024-11-19':

    ymin  = -5.5 if np.abs(dfekf['pred_acc_y'].min() + 5.5)>=2 else dfekf['pred_acc_y'].min()
    ymax  =  5.5 if np.abs(dfekf['pred_acc_y'].max() - 5.5)>=2 else dfekf['pred_acc_y'].max()
    ymean =  0.0 if np.abs(dfekf['pred_acc_y'].mean()- 0) >=0.5 else dfekf['pred_acc_y'].mean()
    yy = np.array([ymin-0.1,ymin*0.7,ymean,ymax*0.7,ymax+0.1])
    xx = np.array([0,0.5,1.4,0.5,0])
    dfekf['x_scale'] = np.interp(dfekf['pred_acc_y'],yy,xx)
    # # df.loc[df['pred_acc_x']<0.0,'x_scale']=1.0
    # # df.loc[df['x_scale']<0.0,'x_scale']=0.0]
    # # df['scale_acc_x'] = df['pred_acc_x']
    dfekf['scale_acc_x'] = dfekf['pred_acc_x']*dfekf['x_scale']
    if dfekf.shape[0]>20: # must >18 G9M0RKAK2YSS_2023-01-30, for LPF
        dfekf['scale_acc_x'] =  filtfilt( lpf05[0], lpf05[1],dfekf['scale_acc_x'].to_numpy())
        dfekf['pred_acc_y'] =  filtfilt( lpf20[0], lpf20[1],dfekf['pred_acc_y'].to_numpy())
    dfekf['nyy']        =  dfekf['pred_acc_y']*dfekf['pred_acc_y']
    dfekf['nxx']        =  dfekf['scale_acc_x']*dfekf['scale_acc_x']
    scale_x = ( np.sqrt( dfekf['nxx'].sum() ) + dict_info['wf_offset_accx']*np.sqrt(dfekf['nxx'].count()) )/np.sqrt(dfekf['nxx'].sum())
    scale_y = ( np.sqrt( dfekf['nyy'].sum() ) + dict_info['wf_offset_accy']*np.sqrt(dfekf['nyy'].count()) )/np.sqrt(dfekf['nyy'].sum())
    dfekf['wfeq_acc_x'] =  dfekf['scale_acc_x']*scale_x
    dfekf['wfeq_acc_y'] =  dfekf['pred_acc_y']*scale_y
    dfekf=dfekf[ (dfekf['wfeq_acc_x'].between(-6.5,6.5) ) & (dfekf['wfeq_acc_y'].between(-6.5,6.5)) ]
    if dict_info['csv_wfeq']:
        csvname ='_'.join([dict_info['serialnumber'],dict_info['datestr'],'wfeq.csv'])
        dfekf.index.name='datetime'
        dfekf.to_csv(csvname)

    if dict_info['png_wfeq']:
        plt_title='_'.join([dict_info['serialnumber'],dict_info['datestr'],'wfeq'])
        png = visual_g2ak_acc(dfekf,plt_title)

    #     -------------- histogram  calculation --------------------------
    #  _   _   _____   _____   _____   _____   _____  ______    ___   ___  ___
    # | | | | |_   _| /  ___| |_   _| |  _  | |  __ \ | ___ \  / _ \  |  \/  |
    # | |_| |   | |   \ `--.    | |   | | | | | |  \/ | |_/ / / /_\ \ | .  . |
    # |  _  |   | |    `--. \   | |   | | | | | | __  |    /  |  _  | | |\/| |
    # | | | |  _| |_  /\__/ /   | |   \ \_/ / | |_\ \ | |\ \  | | | | | |  | |
    # \_| |_/  \___/  \____/    \_/    \___/   \____/ \_| \_| \_| |_/ \_|  |_/


    dfekf = dfekf.reset_index(names='datetime')

    dfekf['kph']    = (dfekf['speed']*3.60).round(0).astype(int)
    dfekf['tenXax'] = (dfekf['wfeq_acc_x']*10.0).round(0).astype(int)
    dfekf['tenXay'] = (dfekf['wfeq_acc_y']*10.0).round(0).astype(int)

    dfgrp = dfekf.groupby(by=['kph','tenXax','tenXay']).agg(DICT_HISTO_AGG)
    dfgrp = dfgrp.reset_index(drop=True)

    dfgrp=dfgrp.rename(columns=DICT_HISTO_RENAME)
    latlon_cols = ['longitude','latitude']
    histo_cols = ['speed', 'coordx','coordy', 'acc_x','acc_y','dist_gps','dist_speed']
    dfgrp [histo_cols]  =  dfgrp [histo_cols].round(3)
    dfgrp [latlon_cols] =  dfgrp [latlon_cols].round(7)
    dfgrp ['datetime']  =  dfgrp ['datetime'].dt.round('1s')

    tseq_cols = histo_cols +['ax_g2ak','ay_g2ak']
    dfekf = dfekf.rename(columns=DICT_TIMESEQ_RENAME)
    # print(dfekf.columns)
    # in_cols=[xx for xx in DICT_TIMESEQ_RENAME.values()]
    # print (dfgrp)
    # print (dfekf)
    in_cols = tseq_cols+['datetime'] +latlon_cols
    dropcols = [xx for xx in dfekf.columns  if not xx in in_cols ]
    dfekf=dfekf.drop(dropcols,axis=1)
    dfekf [tseq_cols]  =  dfekf [tseq_cols].round(3)
    dfekf [latlon_cols] =  dfekf [latlon_cols].round(7)

    dict_info.update({'nhist':dfgrp.shape[0],'ntseq':dfekf.shape[0], })

    return {'df_time':dfekf,'df_hist':dfgrp,'info': dict_info}
# def plot_gap(df,dfgap,df)
def resample_geotab_logrecord(df,dict_info):

    '''
    require: 'datetime', 'latitude', 'longitude','speed'
    index: datetime
    '''
    # df=df.sort_values(by='datetime',ignore_index=True)
    dict_info.update({'utmzone' : int((186+ df['longitude'].mean() )/6)})
    df=calc_deg2xy(df)
    # print (df.shape)
    dfdtime = df[df['speed']>0].index.to_series().diff().dt.total_seconds().fillna(0)
    # dfdv = df.loc[df['speed']>0,'speed'].diff().fillna(1)
    # print (dfdtime.shape,dfdv.shape)
    dfgap   = (dfdtime<300).astype(int)
    # print (dfgap.shape)


    dist_spd = df['dist_spd_cumsum'].max()
    dist_gps = df['dist_gps_cumsum'].max()
    if dist_spd<10 or dist_gps<10:
        return {'df_stat':EMPTY_DF,'df_resample':EMPTY_DF}
        # return DF_EMPTY
    dist_diff_pct = (dist_spd-dist_gps)/(dist_spd+0.001)*100
    if dist_diff_pct>2.5:
        dict_info.update({'firmware_cat':'gps_missing'})
    elif dist_diff_pct<-2.5:
        dict_info.update({'firmware_cat':'spd_zfill'})
    else:
        dict_info.update({'firmware_cat':'normal'})

    if abs(dist_diff_pct)>2.5:
        df.loc[df['dist_gps_interval']==0.0,'speed']=0.0
    if dict_info['png_gps_org']:
        plt_title='_'.join([dict_info['serialnumber'],dict_info['datestr'],'xy_spd_org'])
        png = visual_xy_speed(df,plt_title)
    if dict_info['csv_gps_org']:
        csv_name='_'.join([dict_info['serialnumber'],dict_info['datestr'],'xy_spd_org.csv'])
        df.to_csv(csv_name)

    df_stat= df.loc[df['dspeed']>0,  ['dtime','dspeed','diff_dist','speed']].describe().T
    df_stat['spd>0']=df[df['speed']>0].shape[0]

    # if dict_info['csv_stats']:
    df_stat['serialnumber']   = dict_info['serialnumber']
    df_stat['datestr']        = dict_info['datestr']
    df_stat['nrecords']       = df.shape[0]
    df_stat['dist_spd']       = dist_spd
    df_stat['dist_gps']       = dist_gps
    df_stat['dist_diff_pct']  = dist_diff_pct

    if df_stat.loc['dspeed','min']<0.2: # typical speed resolution is 2 kph
        ''' place holder for G9DD21039966 2024-10-18 '''
        # dict_info.update({'firmware_cat':'G9DD21039966_2024-10-18'})
        dict_info.update({'firmware_cat':'interpolation only'})
        # f,ax=plt.subplots(1,1)
        dfdv = df.loc[df['speed']>0,'speed'].diff().fillna(1)
        # dfdv.plot(ax=ax)
        print (dict_info['serialnumber'],dict_info['datestr'],'bad data set, trying to fix ...')
        # dfgap   = ( (dfdtime>300) | ( ((dfdtime-60).abs()<0.1) & (dfdv.abs()<=0.5)) )
        # dfgap1   =   (dfdtime<300).astype(int)
        dfgap    = (dfdtime>300) |  (((dfdtime-59.999).abs()<0.1) & (dfdv.abs()<=0.5))
        # dfgap   =   dfdv.abs()<=0.2
        # dfgap=dfgap.map({False:1,True:0})
        # dfgap   = df['dist_gps_interval']==0.00
        dfgap=dfgap.map({False:1,True:0})
        # dfdtime.plot.scatter(ax=ax)
        # axt=ax.twinx()
        # axt.scatter(dfdtime.index,dfdtime,s=1,color='g')
        # ax.scatter(dfgap.index,dfgap,s=1,color='r')
        # ax.scatter(dfgap1.index,dfgap1,s=1,color='b')
        #  ax.plot(dfgap.index,dfgap*0)
        # plt.show()
        # sys.exit()
        # dfgps = df.loc[df['dist_gps_interval']>0.0,['x','y','latitude','longitude']].copy()




    if df_stat.loc['dtime','spd>0']<10:
        print (dict_info['serialnumber'],dict_info['datestr'],df[df['speed']>0].shape,'not enough active movements to continue')
        return {'df_stat':df_stat,'df_resample':pd.DataFrame()}

    if  df_stat.loc['dspeed','75%']>50.0/3.6:
        #issues found with ntb-6060, to many 0 speed mixed in the signal
        # dfspd = pd.concat([df[df['dspeed'].abs().between(0,5,inclusive="neither") ],df[df['speed']>10/3.6]] ).reset_index().drop_duplicates(subset='datetime', keep='first').set_index('datetime').sort_index()['speed']
        ''' # # # = =  Kejing Li 2024-12-06 16:17:14  Fri WK#: 49 = =  # # #
        dfspd = pd.concat([df[df['speed']>5/3.6],df.loc[df.groupby(['latitude', 'longitude'])['speed'].idxmax()] ] ).reset_index().drop_duplicates(subset='datetime', keep='first').set_index('datetime').sort_index()['speed']
        dfspd = pd.concat([df.loc[df.groupby(['latitude', 'longitude'])['speed'].idxmax()],df[df['speed']>10.0] ] ).reset_index().drop_duplicates(subset='datetime', keep='first').set_index('datetime').sort_index()['speed']
        dfspd = df.loc[df.groupby(['latitude', 'longitude','trip_num'])['speed'].idxmax()].copy()
        '''
        # spd_zfill
        # dict_info.update({'firmware_cat':'G9F821184285_2024-11-20'})
        dict_info.update({'firmware_cat':'spd_zfill'})
    elif df_stat.loc['dtime','50%']<2.0:
        # place hold if time stamp > Nov. 19, 2024
        # very smalll incremental
        # dfspd =df['speed'].copy()
        dict_info.update({'firmware_cat':'same entropy'})
        # dict_info.update({'firmware_cat':'G9107582NNCK_2024-11-19'})
    else:
        # dfspd =df['speed'].copy()
        '''new scenarios goes here'''
        pass


    # print (dfgap)
    # idx1 = (df['dist_gps_interval']==0) & (df['speed'] > 0)
    # idx2 = (df['dist_gps_interval'] >0) & (df['speed']== 0)
    # idx3 =  df['dspeed'].abs()>10.0
    # idx4 = (df['dspeed']== 0.0) &(df['speed']==0.0)
    # df['trip_num']=np.cumsum(np.where(df['dtime']>300,1,0))
    # print (df.columns)
    in_cols = ['latitude','longitude', 'speed','x','y']
    # dropcols = [xx for xx in df.columns  if not xx in in_cols ]
    df = df.loc[~( ((df['dist_gps_interval']==0) & (df['speed'] > 0) )|( (df['dist_gps_interval'] >0) & (df['speed']== 0))|(df['dspeed'].abs()>10.0)|((df['dspeed']== 0.0) &(df['speed']==0.0) )),in_cols]
    # df = df.drop(dropcols,axis=1)
    if df.shape[0]<5:
        print (dict_info['serialnumber'],dict_info['datestr'],df.shape,'not enough consistent data to continue')
        return {'df_stat':df_stat,'df_resample':pd.DataFrame()}
        # G9C521219F62_2024-07-05
    new_idx   = pd.date_range(df.index.min().round('1s'),df.index.max().round('1s'),freq='s')
    dfmerge   = df.reindex(df.index.union(new_idx)).interpolate('index').reindex(new_idx)
    dfgap1hz  = dfgap.reindex(dfgap.index.union(new_idx)).bfill().reindex(new_idx)
    # dfmerge []   = df.reindex(df.index.union(new_idx)).interpolate('index').reindex(new_idx)
    # dfspd   = dfspd.reindex(dfspd.index.union(new_idx)).interpolate('index').reindex(new_idx)
    # dfgap   = (df['dtime']<300.0).astype(int)
    # dfgap   = dfgap.reindex(dfgap.index.union(new_idx)).bfill().reindex(new_idx)
    # dfgps   = dfgps.reindex(dfgps.index.union(new_idx)).interpolate('index').reindex(new_idx)
    # dfspd   = dfspd.reindex(dfspd.index.union(new_idx)).interpolate('index').reindex(new_idx)
    # dfgap   = (df['dtime']<300.0).astype(int)
    # dfgap   = dfgap.reindex(dfgap.index.union(new_idx)).bfill().reindex(new_idx)
    # dfmerge = pd.merge(dfspd,dfgps,left_index=True,right_index=True)
    # dfmerge.index.name = 'datetime'
    dfmerge['speed']=dfmerge['speed']*dfgap1hz
    dfmerge = calc_deg2xy(dfmerge)
    #  --- does not really help in the final calculation, just for visualization
    # dfmerge = dfmerge.replace('inf', 0.0)
    # print (dfmerge.iloc[0,0])
    # df.replace([np.inf, -np.inf], np.nan, inplace=True)
    idx_missing_speed=(dfmerge['speed']==0) & (dfmerge['dist_gps_interval']>dfmerge['dist_spd_interval']) &(dfmerge['dtime']>0)
    dfmerge.loc[idx_missing_speed,'speed']=dfmerge.loc[idx_missing_speed,'dist_gps_interval']/dfmerge.loc[idx_missing_speed,'dtime']
    dfmerge.loc[dfmerge['dist_gps_interval']==0,'speed'] = 0
    #dfmerge.loc[idx_missing_speed,'dist_gps_interval']/dfmerge.loc[idx_missing_speed,'dtime']
    dfmerge = dfmerge.dropna()

    if dict_info['png_gps_int']:
        plt_title='_'.join([dict_info['serialnumber'],dict_info['datestr'],'xy_spd_int'])
        png = visual_xy_speed(dfmerge,plt_title)
    if dict_info['csv_gps_int']:
        csv_name='_'.join([dict_info['serialnumber'],dict_info['datestr'],'xy_spd_int.csv'])
        dfmerge.to_csv(csv_name)
    in_cols = [ 'latitude','longitude','x','y', 'speed','dist_gps_interval','dist_spd_interval']
    dropcols = [xx for xx in dfmerge.columns  if not xx in in_cols ]
    dfmerge=dfmerge.drop(dropcols,axis=1)
    return {'df_stat':df_stat,'df_resample':dfmerge}

def calc_acc_kalman(dfspk,csvpath='',dict_info={}):
    '''
    main function,    process control

    '''
    dict_agg = {
                     'latitude'  : 'mean',
                    'longitude'  : 'mean',
                        'speed'  : 'mean',
                 'serialnumber'  : 'first',
                      'groupid'  : 'first',
                    'groupname'  : 'first',
                         'date'  : 'first',
                          'vin'  : 'first'
                         }

    DICT_G2AK_STATS_RENAME={
                        'count'	: 'nspdchanges',
                         'mean' : 'meanval',
                          'std'	: 'stddev',
                          'min'	: 'minval',
                          '25%'	: 'pct25val',
                          '50%'	: 'pct50val',
                          '75%'	: 'pct75val',
                          'max'	: 'maxval',
                      'datestr'	: 'date',
                        'spd>0' : 'nspdnonzero',    }


    if os.path.isfile(csvpath):
        dfall = pd.read_csv(csvpath,dtype=object)
        dfall.columns  = dfall.columns.str.lower()
        dfall['datetime']=pd.to_datetime(dfall['datetime'])
    else:
        # name for databricks when need to save the intermediate data pngs or csvs
        dfall=dfspk
        dfall.columns  = dfall.columns.str.lower()
        csvpath ='_'.join(['geotab_logrecord',dfall['datetime'].min().strftime('%Y-%m-%d'),dfall['datetime'].max().strftime('%Y-%m-%d')])+'.csv'

    if dfall.shape[0]<10:
        print (dfall.shape,'curent dataframe/query does not have enough datapoints to continue... skip... ')
        return False

    dfall=dfall.rename(columns={'vehicleidentificationnumber':'vin'})
    cols_requrired = ['serialnumber','datetime', 'latitude', 'longitude','speed','groupid','groupname','date','vin']
    cols_todropp   = [xx for xx in dfall.columns  if not xx in cols_requrired ]
    col_check      = all(col in dfall.columns for col in cols_requrired)

    if not col_check:
        print ('columns are required:', ','.join(cols_requrired))
        print ('curent dataframe/query does not enough column information to continue... skip... ')
        return False

    dfall=dfall.drop(cols_todropp,axis=1)
    dfstats  = []
    dftseqs  = []
    dfhists  = []
    # keep the default flag infomation
    dict_info_default=dict_info.copy()
    # dict_info = reset_dict_info()

    if dict_info ['debug_mode']:
        sns_inc  = dict_info['debug_sns']
        days_inc = dict_info['debug_dates']
    else:
        sns_inc  = []
        days_inc = []
    dfall['date'] = dfall['date'].astype(str)
    for datestr in dfall['date'].unique():
        for sn in dfall['serialnumber'].unique():
            dict_info = dict_info_default.copy()
            df=dfall[ (dfall['serialnumber']==sn)&(dfall['date']==datestr) ].copy()
            df=df.sort_values(by='datetime',ignore_index=True)
            if df.shape[0]<10:
                print (sn,datestr,df.shape,'not enough data points to continue ... ')
                continue
            # df=df.set_index('datetime')
            # latlen = df['latitude'].str.len()
            # spdlen = df['speed'].str.len()
            # latlenpct = df[latlen>=12].shape[0]/df.shape[0]
            # spdlenpct = df[spdlen>=5].shape[0]/df.shape[0]
            # gps_int_flg = True if latlenpct >0.2 else False
            # spd_int_flg = True if spdlenpct >0.2 else False
            # print (f"gps_int_flg:{gps_int_flg}, spd_int_flg:{spd_int_flg}")
            df[['speed','latitude','longitude']] = df[['speed','latitude','longitude']].astype(float)
            df['speed'] =  df['speed']/3.6
            df['datetime'] =  df['datetime'].dt.round('10ms')
            if not df['datetime'].is_unique:
                print (f'{sn} {datestr}, datetime column is not unique')
                df=df.groupby('datetime').agg(dict_agg)
            else:
                df=df.set_index('datetime')
            if df.shape[0]<10:
                print (sn,datestr,df.shape,'not enough data points to continue ... ')
                continue
            # print(df)
            dict_info.update({   'serialnumber' :  sn,
                                 'datestr'      :  datestr,
                                 'groupname'    :  df['groupname'].iloc[0],
                                 'groupid'      :  df['groupid'].iloc[0],
                                 'vin'          :  df['vin'].iloc[0],
                              })

            if len(sns_inc)>0 and ( not sn in sns_inc):
                continue
            if len(days_inc)>0  and (not datestr in days_inc):
                continue
            print (sn,datestr,df.shape,'continue to resample ...')
            # resample step
            dict_dfs    =  resample_geotab_logrecord(df,dict_info)
            df_resample = dict_dfs['df_resample']
            df_stat     =  dict_dfs['df_stat']
            df_stat['firmware_cat']  = dict_info['firmware_cat']

            # calculation step
            dict_dfs    = g2ak_calculation(df_resample,dict_info)
            df_time  =  dict_dfs['df_time']
            df_time['serialnumber']   = sn
            df_time['date']  = datestr

            df_hist  =  dict_dfs['df_hist']
            df_hist['serialnumber']   = sn
            df_hist['date'] = datestr

            if dict_info['csv_hist'] and dict_info['csv_wfeq']:
                # '''only when small sample'''
                dftseqs.append(df_time)
                dfhists.append(df_hist)

            if dict_info['write_hist']:
                write_histogram_table(df_hist,dict_info)


            if dict_info['write_tseq']:
                write_timehistory_table(df_time,dict_info)



            df_stat = df_stat.rename(columns = DICT_G2AK_STATS_RENAME)
            df_stat = df_stat.reset_index(names = 'category')

            df_stat['nhist']     =  df_hist.shape[0]
            df_stat['ntseq']     =  df_time.shape[0]
            df_stat['groupname'] =  dict_info['groupname']
            df_stat['utmzone']   =  dict_info['utmzone']

            if dict_info['write_stat']:
                write_g2akstat_table(df_stat,dict_info)

            if dict_info['csv_stat']:
                dfstats.append(df_stat)

    # run outside of databricks save as csv files
    if dict_info['csv_stat'] and (len(dfstats) >0):
        dfstats_all = pd.concat(dfstats)
        staname=os.path.basename(csvpath)[:-4]+'_stats.csv'
        dfstats_all.to_csv(staname)

    if dict_info['csv_wfeq'] and dict_info['write_hist']  and  (len(dftseqs) >0) and  (len(dfhists) >0) :
        dftseqs_all = pd.concat(dftseqs)
        dftseqs_all.index.name = 'datetime'
        dfhists_all = pd.concat(dfhists)
        basename = os.path.basename(csvpath)
        dftseqs_all.to_csv(basename[:-4]+'_g2ak.csv')
        dfhists_all.to_csv(basename[:-4]+'_hist.csv',index=False)

    if dict_info['png_hist2d']:
        for sn in dftseqs_all['serialnumber'].unique():
            dft= dftseqs_all[dftseqs_all['serialnumber']==sn]
            listofAX=[dft['wfeq_acc_x'].to_numpy()]
            listofAY=[dft['wfeq_acc_y'].to_numpy()]
            listofTitles=['G2Ak Calculation']
            ftitle=basename[:-4]+f'_{sn}_his2d'
            visual_hist2d(listofAX,listofAY,listofTitles,ftitle)
    return True
# COMMAND ----------