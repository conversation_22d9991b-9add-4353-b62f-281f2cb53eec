# LOGRECORD = 'tm_sandbox_catalog.geotab_sales_eng.logrecord'
LOGRECORD = 'd2evc.geotab_sales_eng.logrecord'
SRC_TABLE = LOGRECORD
# output tables
G2AK_DB_SCHEMA       = 'wear10_sandbox.g2ak'
TABLE_HISTOGRAM      = 'acceleration_histogram'
TABLE_TIMEHISTORY    = 'acceleration_timehistory'
TABLE_G2AKSTAT       = 'calculation_status'
TABLE_REQUEST        = 'calculation_request'

SCHEMA_TIMEHISTORY = f"""     CREATE TABLE IF NOT EXISTS {G2AK_DB_SCHEMA}.{TABLE_TIMEHISTORY} (
        groupid            string comment 'group id',
        groupname          string comment 'group name',
        serialnumber       string comment 'serial number of the device geotab or xee',
        vin                string comment 'vehicle identification number',
        date                 date comment 'data capture date',
        datetime        timestamp comment 'timestamp of the record',
        messagetype        string comment 'time history data',
        latitude           double comment 'latitude   - degree',
        longitude          double comment 'longitude  - degree',
        coordx             double comment 'latitude coordinate  - m',
        coordy             double comment 'longitude coordinate  - m',
        speed              double comment 'speed   - m/s',
        acc_x              double comment 'longitudinal acceleration  - m/sec^2,webfleet equivialent',
        acc_y              double comment 'lateral acceleration  - m/sec^2,webfleet equivialent',
        dist_gps           double comment 'distance drive for current interval from gps distance- m',
        dist_speed         double comment 'distance drive for current interval from speed x time- m',
        ax_g2ak            double comment 'longitudinal acceleration  - m/sec^2',
        ay_g2ak            double comment 'lateral acceleration  - m/sec^2',
        createdtime     timestamp comment 'timestamp of when data ingested')"""

SCHEMA_HISTOGRAM  = f"""  CREATE TABLE IF NOT EXISTS {G2AK_DB_SCHEMA}.{TABLE_HISTOGRAM} (
        groupid            string comment 'group id',
        groupname          string comment 'group name',
        serialnumber       string comment 'serial number of the device geotab or xee',
        vin                string comment 'vehicle identification number',
        date                 date comment 'data capture date',
        datetime        timestamp comment 'mean time of current combination record',
        messagetype        string comment 'histogram data',
        latitude           double comment 'mean latitude  of the day - degree',
        longitude          double comment 'mean longitude of the day - degree',
        speed              double comment 'speed  -  m/s',
        acc_x              double comment 'longitudinal acceleration in m/sec^2',
        acc_y              double comment 'lateral acceleration in m/sec^2',
        count                 int comment 'count of histogram data,value of a sparse 3d matrix',
        dist_gps           double comment 'sum of the distance drive (m) from gps calculation',
        dist_speed         double comment 'sum of the distance of drive (m) from speed calculation',
        createdtime     timestamp comment 'timestamp of when data ingested')"""

SCHEMA_G2AKSTAT = f"""  CREATE TABLE  IF NOT EXISTS {G2AK_DB_SCHEMA}.{TABLE_G2AKSTAT} (
        groupid            string comment 'group id',
        serialnumber       string comment 'serial number of the device geotab or xee',
        groupname          string comment 'group name',
        date                 date comment 'data capture date',
        vin                string comment 'vehicle identification number',
        messagetype        string comment 'conversion file',
        category           string comment 'analytic variables',
        nspdchanges           int comment 'mumber of records of speed changes',
        meanval            double comment 'average value of current variable',
        stddev             double comment 'standard deviation current variable',
        minval             double comment 'min value of current variable',
        pct25val           double comment '25 percent value of current variable',
        pct50val           double comment 'median value of current variable',
        pct75val           double comment '75 percent value of current variable',
        maxval             double comment 'max value of current variable',
        nspdnonzero           int comment 'number of records of non-zero speed',
        nrecords              int comment 'number of records of that day and that serial number',
        nhist                 int comment 'number of records of time history',
        ntseq                 int comment 'number of records of histogram',
        utmzone               int comment 'longidtude zone location',
        firmware_cat       string comment 'issues with the firmware',
        dist_gps           double comment 'distance drive for current interval from gps distance- m',
        dist_spd           double comment 'distance drive for current interval from speed x time- m',
        dist_diff_pct      double comment '(dist_spd-dist_gps) / dist_spd * 100',
        createdtime     timestamp comment 'timestamp of when data ingested' )"""

SCHEMA_REQUEST = f"""  CREATE TABLE  IF NOT EXISTS {G2AK_DB_SCHEMA}.{TABLE_REQUEST} (
         requestdate      date  comment 'date when requested',
           requestor    string  comment 'the person who requested',
             groupid    string  comment 'geotab group id',
           groupname    string  comment 'geotab group name ',
        serialnumber    string  comment 'geotab serial number',
           datestart      date  comment 'first day to calculate',
             dateend      date  comment 'last  day to calculate',
              status    string  comment 'current status of this request',
        createdtime  timestamp  comment 'timestamp of when data ingested' ) """